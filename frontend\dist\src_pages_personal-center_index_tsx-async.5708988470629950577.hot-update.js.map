{"version": 3, "sources": ["src_pages_personal-center_index_tsx-async.5708988470629950577.hot-update.js", "src/pages/personal-center/PersonalInfo.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/personal-center/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='17297680223962044828';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"src/pages/personal-center/index.tsx\"],\"src/pages/settings/index.tsx\":[\"p__settings__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "import {\n  QuestionCircleOutlined,\n  SettingOutlined,\n} from '@ant-design/icons';\nimport {\n  Alert,\n  Grid,\n  Space,\n  Spin,\n  Typography,\n} from 'antd';\nimport { ProCard } from '@ant-design/pro-components';\n\nimport React, { useEffect, useState } from 'react';\nimport { UserService } from '@/services/user';\nimport type { UserProfileDetailResponse } from '@/types/api';\nimport UnifiedSettingsModal from './UnifiedSettingsModal';\nimport UserInfoPopover from './UserInfoPopover';\n\nconst { Title, Text } = Typography;\n\n/**\n * 个人信息组件\n *\n * 显示用户的基本个人信息，采用简洁的卡片设计。\n * 用户名支持悬浮显示详细信息（电话、邮箱、注册时间等）。\n *\n * 主要功能：\n * 1. 显示用户头像（使用姓名首字母）\n * 2. 显示用户姓名（支持气泡卡片显示详细信息）\n * 3. 显示最后登录时间和登录团队\n * 4. 提供设置入口\n *\n * 响应式设计：\n * - xs/sm: 移动端优化，紧凑布局\n * - md+: 桌面端，标准布局\n *\n * 数据来源：\n * - 用户详细信息：通过UserService.getUserProfileDetail()获取\n */\nconst PersonalInfo: React.FC = () => {\n  /**\n   * 响应式检测\n   */\n  const { useBreakpoint } = Grid;\n  const screens = useBreakpoint();\n  /**\n   * 用户详细信息状态管理\n   */\n  const [userInfo, setUserInfo] = useState<UserProfileDetailResponse>({\n    name: '',\n    position: '',\n    email: '',\n    telephone: '',\n    registerDate: '',\n    lastLoginTime: '',\n    lastLoginTeam: '',\n    teamCount: 0,\n    avatar: '',\n  });\n\n  const [userInfoLoading, setUserInfoLoading] = useState(true);\n  const [userInfoError, setUserInfoError] = useState<string | null>(null);\n\n  // Modal状态管理\n  const [settingsModalVisible, setSettingsModalVisible] = useState(false);\n\n  // 获取用户数据\n  useEffect(() => {\n    const fetchUserData = async () => {\n      try {\n        const userDetail = await UserService.getUserProfileDetail();\n        setUserInfo(userDetail);\n        setUserInfoError(null);\n      } catch (error) {\n        console.error('获取用户详细信息失败:', error);\n        setUserInfoError('获取用户详细信息失败，请稍后重试');\n      } finally {\n        setUserInfoLoading(false);\n      }\n    };\n\n    fetchUserData();\n  }, []);\n\n  return (\n    <>\n      <ProCard\n        style={{\n          marginBottom: 24,\n          borderRadius: 16,\n          border: '1px solid rgba(37, 99, 235, 0.08)',\n          position: 'relative',\n          background: 'linear-gradient(135deg, #ffffff 0%, #fafbff 100%)',\n          boxShadow: '0 4px 20px rgba(37, 99, 235, 0.06)',\n        }}\n        bodyStyle={{\n          padding: screens.md ? '28px 24px' : '20px 16px',\n        }}\n      >\n        {/* 右上角图标区域 */}\n        <div style={{\n          position: 'absolute',\n          top: screens.md ? '20px' : '16px',\n          right: screens.md ? '20px' : '16px',\n          zIndex: 10,\n        }}>\n          <Space size={screens.md ? 16 : 12}>\n            <UserInfoPopover userInfo={userInfo}>\n              <QuestionCircleOutlined\n                style={{\n                  fontSize: screens.md ? 20 : 18,\n                  color: '#6b7280',\n                  cursor: 'pointer',\n                  transition: 'all 0.3s ease',\n                  padding: '8px',\n                  borderRadius: '8px',\n                  background: 'rgba(37, 99, 235, 0.04)',\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.color = '#2563eb';\n                  e.currentTarget.style.background = 'rgba(37, 99, 235, 0.1)';\n                  e.currentTarget.style.transform = 'scale(1.05)';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.color = '#6b7280';\n                  e.currentTarget.style.background = 'rgba(37, 99, 235, 0.04)';\n                  e.currentTarget.style.transform = 'scale(1)';\n                }}\n              />\n            </UserInfoPopover>\n            <SettingOutlined\n              style={{\n                fontSize: screens.md ? 20 : 18,\n                color: '#6b7280',\n                cursor: 'pointer',\n                transition: 'all 0.3s ease',\n                padding: '8px',\n                borderRadius: '8px',\n                background: 'rgba(37, 99, 235, 0.04)',\n              }}\n              onMouseEnter={(e) => {\n                e.currentTarget.style.color = '#2563eb';\n                e.currentTarget.style.background = 'rgba(37, 99, 235, 0.1)';\n                e.currentTarget.style.transform = 'scale(1.05)';\n              }}\n              onMouseLeave={(e) => {\n                e.currentTarget.style.color = '#6b7280';\n                e.currentTarget.style.background = 'rgba(37, 99, 235, 0.04)';\n                e.currentTarget.style.transform = 'scale(1)';\n              }}\n              onClick={() => setSettingsModalVisible(true)}\n            />\n          </Space>\n        </div>\n\n        {userInfoError ? (\n          <Alert\n            message=\"个人信息加载失败\"\n            description={userInfoError}\n            type=\"error\"\n            showIcon\n            style={{\n              borderRadius: 12,\n              border: 'none',\n            }}\n          />\n        ) : (\n          <Spin spinning={userInfoLoading}>\n            {/* 问候区域 */}\n            <div style={{\n              display: 'flex',\n              justifyContent: 'center',\n              alignItems: 'center',\n              padding: screens.md ? '16px 0' : '12px 0',\n              marginTop: screens.md ? '20px' : '16px', // 为右上角图标留出空间\n            }}>\n              {/* 问候语 */}\n              <Typography.Title\n                level={screens.md ? 3 : 4}\n                style={{\n                  margin: 0,\n                  fontSize: screens.md ? 24 : 20,\n                  color: '#1f2937',\n                  textAlign: 'center',\n                  fontWeight: 600,\n                  background: 'linear-gradient(135deg, #2563eb 0%, #3b82f6 100%)',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent',\n                  backgroundClip: 'text',\n                }}\n              >\n                您好，{userInfo.name || '加载中...'}\n              </Typography.Title>\n            </div>\n          </Spin>\n        )}\n      </ProCard>\n\n      {/* 统一设置Modal */}\n      <UnifiedSettingsModal\n        visible={settingsModalVisible}\n        onCancel={() => setSettingsModalVisible(false)}\n        userInfo={userInfo}\n        onSuccess={() => {\n          // 可以在这里刷新用户信息\n          console.log('设置操作成功');\n        }}\n      />\n    </>\n  );\n};\n\nexport default PersonalInfo;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uCACA;IACE,SAAS;;;;;;wCCkNb;;;2BAAA;;;;;;;0CAlNO;yCAOA;kDACiB;oFAEmB;yCACf;kGAEK;6FACL;;;;;;;;;;YAE5B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;YAElC;;;;;;;;;;;;;;;;;;CAkBC,GACD,MAAM,eAAyB;;gBAC7B;;GAEC,GACD,MAAM,EAAE,aAAa,EAAE,GAAG,UAAI;gBAC9B,MAAM,UAAU;gBAChB;;GAEC,GACD,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAA4B;oBAClE,MAAM;oBACN,UAAU;oBACV,OAAO;oBACP,WAAW;oBACX,cAAc;oBACd,eAAe;oBACf,eAAe;oBACf,WAAW;oBACX,QAAQ;gBACV;gBAEA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAC;gBACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAgB;gBAElE,YAAY;gBACZ,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,IAAA,eAAQ,EAAC;gBAEjE,SAAS;gBACT,IAAA,gBAAS,EAAC;oBACR,MAAM,gBAAgB;wBACpB,IAAI;4BACF,MAAM,aAAa,MAAM,iBAAW,CAAC,oBAAoB;4BACzD,YAAY;4BACZ,iBAAiB;wBACnB,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,eAAe;4BAC7B,iBAAiB;wBACnB,SAAU;4BACR,mBAAmB;wBACrB;oBACF;oBAEA;gBACF,GAAG,EAAE;gBAEL,qBACE;;sCACE,2BAAC,sBAAO;4BACN,OAAO;gCACL,cAAc;gCACd,cAAc;gCACd,QAAQ;gCACR,UAAU;gCACV,YAAY;gCACZ,WAAW;4BACb;4BACA,WAAW;gCACT,SAAS,QAAQ,EAAE,GAAG,cAAc;4BACtC;;8CAGA,2BAAC;oCAAI,OAAO;wCACV,UAAU;wCACV,KAAK,QAAQ,EAAE,GAAG,SAAS;wCAC3B,OAAO,QAAQ,EAAE,GAAG,SAAS;wCAC7B,QAAQ;oCACV;8CACE,cAAA,2BAAC,WAAK;wCAAC,MAAM,QAAQ,EAAE,GAAG,KAAK;;0DAC7B,2BAAC,wBAAe;gDAAC,UAAU;0DACzB,cAAA,2BAAC,6BAAsB;oDACrB,OAAO;wDACL,UAAU,QAAQ,EAAE,GAAG,KAAK;wDAC5B,OAAO;wDACP,QAAQ;wDACR,YAAY;wDACZ,SAAS;wDACT,cAAc;wDACd,YAAY;oDACd;oDACA,cAAc,CAAC;wDACb,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;wDAC9B,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;wDACnC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;oDACpC;oDACA,cAAc,CAAC;wDACb,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;wDAC9B,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;wDACnC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;oDACpC;;;;;;;;;;;0DAGJ,2BAAC,sBAAe;gDACd,OAAO;oDACL,UAAU,QAAQ,EAAE,GAAG,KAAK;oDAC5B,OAAO;oDACP,QAAQ;oDACR,YAAY;oDACZ,SAAS;oDACT,cAAc;oDACd,YAAY;gDACd;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;oDAC9B,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;oDACnC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;gDACpC;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;oDAC9B,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;oDACnC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;gDACpC;gDACA,SAAS,IAAM,wBAAwB;;;;;;;;;;;;;;;;;gCAK5C,8BACC,2BAAC,WAAK;oCACJ,SAAQ;oCACR,aAAa;oCACb,MAAK;oCACL,QAAQ;oCACR,OAAO;wCACL,cAAc;wCACd,QAAQ;oCACV;;;;;yDAGF,2BAAC,UAAI;oCAAC,UAAU;8CAEd,cAAA,2BAAC;wCAAI,OAAO;4CACV,SAAS;4CACT,gBAAgB;4CAChB,YAAY;4CACZ,SAAS,QAAQ,EAAE,GAAG,WAAW;4CACjC,WAAW,QAAQ,EAAE,GAAG,SAAS;wCACnC;kDAEE,cAAA,2BAAC,gBAAU,CAAC,KAAK;4CACf,OAAO,QAAQ,EAAE,GAAG,IAAI;4CACxB,OAAO;gDACL,QAAQ;gDACR,UAAU,QAAQ,EAAE,GAAG,KAAK;gDAC5B,OAAO;gDACP,WAAW;gDACX,YAAY;gDACZ,YAAY;gDACZ,sBAAsB;gDACtB,qBAAqB;gDACrB,gBAAgB;4CAClB;;gDACD;gDACK,SAAS,IAAI,IAAI;;;;;;;;;;;;;;;;;;;;;;;sCAQ/B,2BAAC,6BAAoB;4BACnB,SAAS;4BACT,UAAU,IAAM,wBAAwB;4BACxC,UAAU;4BACV,WAAW;gCACT,cAAc;gCACd,QAAQ,GAAG,CAAC;4BACd;;;;;;;;YAIR;eA3KM;iBAAA;gBA6KN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;IDlND;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,gCAA+B;YAAC;SAAqB;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AACrjB"}