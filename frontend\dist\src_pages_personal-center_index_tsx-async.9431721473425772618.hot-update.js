globalThis.makoModuleHotUpdate('src/pages/personal-center/index.tsx', {
    modules: {
        "src/pages/personal-center/index.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
            var _react = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/react/index.js"));
            var _FloatButton = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/components/FloatButton/index.tsx"));
            var _TeamListCard = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/TeamListCard.tsx"));
            var _TodoManagement = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/TodoManagement.tsx"));
            var _PersonalInfo = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/PersonalInfo.tsx"));
            var _DataOverview = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/DataOverview.tsx"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            /**
 * 个人中心页面组件
 *
 * 这是用户的个人中心主页面，提供用户信息管理、团队管理、待办事项等功能。
 * 是用户进行个人设置和团队操作的主要入口页面。
 *
 * 页面功能：
 * 1. 用户个人信息展示和编辑
 * 2. 团队列表显示和团队切换
 * 3. 个人待办事项管理
 * 4. 全局浮动操作按钮
 *
 * 页面结构：
 * - 左列：个人信息、团队列表（响应式布局）
 * - 右列：待办事项管理（响应式布局）
 * - 数据概览：独立的水平卡片组件，位于个人信息下方
 * - 浮动：全局操作按钮
 *
 * 权限控制：
 * - 需要用户登录才能访问
 * - 自动检查登录状态并重定向
 * - 支持登录状态变化的实时响应
 *
 * 响应式设计：
 * - 移动端：垂直堆叠布局
 * - 桌面端：左右分栏布局
 * - 自适应不同屏幕尺寸
 */ const PersonalCenterPage = ()=>{
                _s();
                /**
   * 全局状态管理
   *
   * 从UmiJS全局状态中获取用户信息和加载状态：
   * - loading: 全局状态的加载状态
   */ const { loading } = (0, _max.useModel)('@@initialState');
                /**
   * 加载状态处理
   *
   * 当全局状态正在初始化时，显示加载界面。
   * 这确保了用户在状态加载完成前看到友好的加载提示。
   */ if (loading) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        minHeight: '100vh',
                        background: '#f5f8ff',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center'
                    },
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                            size: "large"
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/index.tsx",
                            lineNumber: 67,
                            columnNumber: 9
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                marginLeft: 16
                            },
                            children: "正在加载用户信息..."
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/index.tsx",
                            lineNumber: 68,
                            columnNumber: 9
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/index.tsx",
                    lineNumber: 58,
                    columnNumber: 7
                }, this);
                /**
   * 登录状态检查已由应用级路由守卫处理
   *
   * 移除页面级别的登录检查，避免与应用级路由守卫冲突。
   * 应用级路由守卫在 app.tsx 中的 onPageChange 函数已经处理了
   * 用户登录状态检查和重定向逻辑，无需在页面组件中重复检查。
   *
   * 这样可以避免登录成功后的状态更新时序问题，确保用户
   * 一次登录成功后能够正常访问个人中心页面。
   */ return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                minHeight: '100vh',
                                background: 'linear-gradient(135deg, #f0f4ff 0%, #e6f0ff 100%)',
                                padding: '16px'
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProCard, {
                                style: {
                                    width: '100%',
                                    minHeight: 'calc(100vh - 32px)',
                                    borderRadius: '16px',
                                    boxShadow: '0 8px 32px rgba(37, 99, 235, 0.08)',
                                    border: '1px solid rgba(37, 99, 235, 0.06)',
                                    background: '#ffffff'
                                },
                                bodyStyle: {
                                    padding: '32px 24px'
                                },
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                    gutter: {
                                        xs: [
                                            16,
                                            16
                                        ],
                                        sm: [
                                            20,
                                            20
                                        ],
                                        md: [
                                            24,
                                            24
                                        ],
                                        lg: [
                                            24,
                                            16
                                        ],
                                        xl: [
                                            32,
                                            20
                                        ],
                                        xxl: [
                                            40,
                                            24
                                        ]
                                    },
                                    style: {
                                        margin: 0
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                            xs: 24,
                                            sm: 24,
                                            md: 24,
                                            lg: 12,
                                            xl: 12,
                                            xxl: 12,
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_PersonalInfo.default, {}, void 0, false, {
                                                    fileName: "src/pages/personal-center/index.tsx",
                                                    lineNumber: 166,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_DataOverview.default, {}, void 0, false, {
                                                    fileName: "src/pages/personal-center/index.tsx",
                                                    lineNumber: 169,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TeamListCard.default, {}, void 0, false, {
                                                    fileName: "src/pages/personal-center/index.tsx",
                                                    lineNumber: 172,
                                                    columnNumber: 15
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/personal-center/index.tsx",
                                            lineNumber: 157,
                                            columnNumber: 13
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                            xs: 24,
                                            sm: 24,
                                            md: 24,
                                            lg: 12,
                                            xl: 12,
                                            xxl: 12,
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TodoManagement.default, {}, void 0, false, {
                                                fileName: "src/pages/personal-center/index.tsx",
                                                lineNumber: 195,
                                                columnNumber: 15
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/index.tsx",
                                            lineNumber: 187,
                                            columnNumber: 13
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/personal-center/index.tsx",
                                    lineNumber: 130,
                                    columnNumber: 11
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/index.tsx",
                                lineNumber: 103,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/index.tsx",
                            lineNumber: 87,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_FloatButton.default, {}, void 0, false, {
                            fileName: "src/pages/personal-center/index.tsx",
                            lineNumber: 211,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true);
            };
            _s(PersonalCenterPage, "MSvZMAQVKk4+6Ll7WBSEu/hOAd0=", false, function() {
                return [
                    _max.useModel
                ];
            });
            _c = PersonalCenterPage;
            var _default = PersonalCenterPage;
            var _c;
            $RefreshReg$(_c, "PersonalCenterPage");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '15114927221884676165';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/settings/index.tsx": [
            "p__settings__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_personal-center_index_tsx-async.9431721473425772618.hot-update.js.map